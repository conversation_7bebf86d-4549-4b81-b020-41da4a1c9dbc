<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Dealer extends Model
{
    use HasFactory;

    protected $fillable = [
        'region_id',
        'name',
        'contact_person',
        'phone',
        'email',
        'address',
        'city',
        'district',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the region that owns the dealer.
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the customers for the dealer.
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the sales representatives for the dealer.
     */
    public function salesRepresentatives()
    {
        return $this->hasMany(User::class)->whereHas('role', function($query) {
            $query->where('name', Role::SALES_REPRESENTATIVE);
        });
    }

    /**
     * Scope a query to only include active dealers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Search scope for dealers
     */
    public function scopeSearch($query, $q)
    {
        if (!$q) return $query;

        return $query->where(function($sub) use ($q) {
            $sub->where('name', 'ILIKE', "%$q%")
                ->orWhere('contact_person', 'ILIKE', "%$q%")
                ->orWhere('phone', 'ILIKE', "%$q%")
                ->orWhere('email', 'ILIKE', "%$q%")
                ->orWhere('city', 'ILIKE', "%$q%")
                ->orWhere('district', 'ILIKE', "%$q%")
                ->orWhereHas('region', function($regionQuery) use ($q) {
                    $regionQuery->where('name', 'ILIKE', "%$q%");
                });
        });
    }
}
