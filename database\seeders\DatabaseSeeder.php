<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
       //DB::statement('TRUNCATE TABLE users RESTART IDENTITY CASCADE');

        // \App\Models\User::factory(10)->create();
        $this->call(RoleSeeder::class);
        $this->call(CustomerSeeder::class);
        $this->call(CustomerFollowupSeeder::class);
        $this->call(PotentialCustomerSeeder::class);
        $this->call(RegionSeeder::class);
        $this->call(DealerSeeder::class);
        $this->call(UserSeeder::class);
        

       User::factory()->create([
           'name' => 'Test User',
           'email' => '<EMAIL>',
           'phone' => '+90 ************',
           'password' => Hash::make('test1234'),
       ]);

        //try {
        //    DB::beginTransaction();
//
        //    $this->command->info('Databases truncated!');
//
        //    $sqlPath = database_path('seeders/users.sql');
//
        //    if (File::exists($sqlPath)) {
        //        $sql = File::get($sqlPath);
//
        //        DB::unprepared($sql);
        //        DB::commit();
//
        //        $this->command->info('users.sql successfully seeded!');
        //    } else {
        //        throw new \Exception('users.sql not found!');
        //    }
        //} catch (\Exception $e) {
        //    DB::rollBack();
        //    $this->command->error('Seeder hatası: ' . $e->getMessage());
        //}
    }
}
