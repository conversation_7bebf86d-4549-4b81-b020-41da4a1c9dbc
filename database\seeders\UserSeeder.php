<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\Dealer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get roles
        $adminRole = Role::where('name', Role::ADMIN)->first();
        $salesRepRole = Role::where('name', Role::SALES_REPRESENTATIVE)->first();
        $managerRole = Role::where('name', Role::MANAGER)->first();

        // Get dealers
        $dealers = Dealer::all();

        // Create admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sistem Yöneticisi',
                'phone' => '+90 ************',
                'password' => Hash::make('admin123'),
                'role_id' => $adminRole->id,
            ]
        );

        // Create manager user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>l <PERSON>üdür',
                'phone' => '+90 ************',
                'password' => Hash::make('manager123'),
                'role_id' => $managerRole->id,
            ]
        );

        // Create sales representatives for each dealer
        $salesRepresentatives = [
            [
                'name' => 'Ahmet Yılmaz',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'İstanbul Merkez Bayi'
            ],
            [
                'name' => 'Mehmet Kaya',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'İstanbul Merkez Bayi'
            ],
            [
                'name' => 'Fatma Demir',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'Bursa Bayi'
            ],
            [
                'name' => 'Ali Özkan',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'Bursa Bayi'
            ],
            [
                'name' => 'Ayşe Şahin',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'Ankara Bayi'
            ],
            [
                'name' => 'Mustafa Çelik',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'İzmir Bayi'
            ],
            [
                'name' => 'Zeynep Yıldız',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'Antalya Bayi'
            ],
            [
                'name' => 'Hasan Polat',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'dealer_name' => 'Adana Bayi'
            ],
        ];

        foreach ($salesRepresentatives as $salesRep) {
            $dealer = $dealers->where('name', $salesRep['dealer_name'])->first();
            
            if ($dealer) {
                User::firstOrCreate(
                    ['email' => $salesRep['email']],
                    [
                        'name' => $salesRep['name'],
                        'phone' => $salesRep['phone'],
                        'password' => Hash::make('sales123'),
                        'role_id' => $salesRepRole->id,
                        'dealer_id' => $dealer->id,
                    ]
                );
            }
        }
    }
}
