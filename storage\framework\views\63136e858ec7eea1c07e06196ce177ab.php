<?php $__env->startSection('content'); ?>
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0"><PERSON><PERSON></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('role-management.index')); ?>">Rol Yönetimi</a></li>
                        <li class="breadcrumb-item active"><?php echo e($role->display_name); ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><?php echo e($role->display_name); ?></h3>
                            <div class="card-tools">
                                <a href="<?php echo e(route('role-management.edit', $role)); ?>" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> Düzenle
                                </a>
                                <a href="<?php echo e(route('role-management.index')); ?>" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> Geri
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">ID:</th>
                                    <td><?php echo e($role->id); ?></td>
                                </tr>
                                <tr>
                                    <th>Rol Adı:</th>
                                    <td><code><?php echo e($role->name); ?></code></td>
                                </tr>
                                <tr>
                                    <th>Görünen Ad:</th>
                                    <td><?php echo e($role->display_name); ?></td>
                                </tr>
                                <tr>
                                    <th>Guard:</th>
                                    <td><code><?php echo e($role->guard_name ?? 'web'); ?></code></td>
                                </tr>
                                <tr>
                                    <th>Kullanıcı Sayısı:</th>
                                    <td>
                                        <span class="badge badge-info"><?php echo e($role->users->count()); ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Oluşturulma:</th>
                                    <td><?php echo e($role->created_at->format('d.m.Y H:i')); ?></td>
                                </tr>
                                <tr>
                                    <th>Son Güncelleme:</th>
                                    <td><?php echo e($role->updated_at->format('d.m.Y H:i')); ?></td>
                                </tr>
                            </table>

                            <?php if($role->description): ?>
                                <div class="mt-3">
                                    <h5>Açıklama:</h5>
                                    <p class="text-muted"><?php echo e($role->description); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Bu Role Sahip Kullanıcılar</h3>
                        </div>
                        <div class="card-body">
                            <?php if($role->users->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Ad Soyad</th>
                                                <th>E-posta</th>
                                                <th>İşlem</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $role->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($user->name); ?></td>
                                                    <td><?php echo e($user->email); ?></td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<?php echo e(route('user-management.show', $user)); ?>" class="btn btn-info btn-xs">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <form action="<?php echo e(route('role-management.remove-role', $user)); ?>" method="POST" style="display: inline;" onsubmit="return confirm('Bu kullanıcının rolünü kaldırmak istediğinizden emin misiniz?')">
                                                                <?php echo csrf_field(); ?>
                                                                <?php echo method_field('DELETE'); ?>
                                                                <button type="submit" class="btn btn-warning btn-xs">
                                                                    <i class="fas fa-user-minus"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <p>Bu role henüz kullanıcı atanmamış.</p>
                                    <a href="<?php echo e(route('user-management.create')); ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus"></i> Kullanıcı Ekle
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/role_management/show.blade.php ENDPATH**/ ?>