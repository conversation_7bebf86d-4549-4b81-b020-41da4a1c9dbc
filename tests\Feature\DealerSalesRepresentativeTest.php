<?php

namespace Tests\Feature;

use App\Models\Dealer;
use App\Models\User;
use App\Models\Role;
use App\Models\Region;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DealerSalesRepresentativeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create([
            'name' => Role::SALES_REPRESENTATIVE,
            'display_name' => 'Satış Temsilcisi',
            'description' => 'Satış temsilcisi'
        ]);

        Role::create([
            'name' => Role::ADMIN,
            'display_name' => 'Yönetici',
            'description' => 'Sistem yöneticisi'
        ]);
    }

    /** @test */
    public function a_dealer_can_have_multiple_sales_representatives()
    {
        // Create a region
        $region = Region::create([
            'name' => 'Test Bölge',
            'description' => 'Test bölgesi',
            'status' => true
        ]);

        // Create a dealer
        $dealer = Dealer::create([
            'region_id' => $region->id,
            'name' => 'Test Bayi',
            'contact_person' => 'Test Kişi',
            'phone' => '+90 ************',
            'email' => '<EMAIL>',
            'address' => 'Test Adres',
            'city' => 'Test Şehir',
            'district' => 'Test İlçe',
            'status' => true,
        ]);

        // Get sales representative role
        $salesRepRole = Role::where('name', Role::SALES_REPRESENTATIVE)->first();

        // Create multiple sales representatives for the dealer
        $salesRep1 = User::create([
            'name' => 'Satış Temsilcisi 1',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => bcrypt('password'),
            'role_id' => $salesRepRole->id,
            'dealer_id' => $dealer->id,
        ]);

        $salesRep2 = User::create([
            'name' => 'Satış Temsilcisi 2',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => bcrypt('password'),
            'role_id' => $salesRepRole->id,
            'dealer_id' => $dealer->id,
        ]);

        $salesRep3 = User::create([
            'name' => 'Satış Temsilcisi 3',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => bcrypt('password'),
            'role_id' => $salesRepRole->id,
            'dealer_id' => $dealer->id,
        ]);

        // Test that dealer has multiple sales representatives
        $this->assertEquals(3, $dealer->salesRepresentatives()->count());
        
        // Test that each sales representative belongs to the dealer
        $this->assertEquals($dealer->id, $salesRep1->dealer_id);
        $this->assertEquals($dealer->id, $salesRep2->dealer_id);
        $this->assertEquals($dealer->id, $salesRep3->dealer_id);

        // Test that sales representatives are correctly retrieved
        $salesReps = $dealer->salesRepresentatives;
        $this->assertCount(3, $salesReps);
        $this->assertTrue($salesReps->contains($salesRep1));
        $this->assertTrue($salesReps->contains($salesRep2));
        $this->assertTrue($salesReps->contains($salesRep3));
    }

    /** @test */
    public function a_sales_representative_belongs_to_a_dealer()
    {
        // Create a region
        $region = Region::create([
            'name' => 'Test Bölge',
            'description' => 'Test bölgesi',
            'status' => true
        ]);

        // Create a dealer
        $dealer = Dealer::create([
            'region_id' => $region->id,
            'name' => 'Test Bayi',
            'contact_person' => 'Test Kişi',
            'phone' => '+90 ************',
            'email' => '<EMAIL>',
            'address' => 'Test Adres',
            'city' => 'Test Şehir',
            'district' => 'Test İlçe',
            'status' => true,
        ]);

        // Get sales representative role
        $salesRepRole = Role::where('name', Role::SALES_REPRESENTATIVE)->first();

        // Create a sales representative
        $salesRep = User::create([
            'name' => 'Satış Temsilcisi',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => bcrypt('password'),
            'role_id' => $salesRepRole->id,
            'dealer_id' => $dealer->id,
        ]);

        // Test that sales representative belongs to the dealer
        $this->assertEquals($dealer->id, $salesRep->dealer->id);
        $this->assertEquals($dealer->name, $salesRep->dealer->name);
    }

    /** @test */
    public function only_sales_representatives_are_returned_by_dealer_relationship()
    {
        // Create a region
        $region = Region::create([
            'name' => 'Test Bölge',
            'description' => 'Test bölgesi',
            'status' => true
        ]);

        // Create a dealer
        $dealer = Dealer::create([
            'region_id' => $region->id,
            'name' => 'Test Bayi',
            'contact_person' => 'Test Kişi',
            'phone' => '+90 ************',
            'email' => '<EMAIL>',
            'address' => 'Test Adres',
            'city' => 'Test Şehir',
            'district' => 'Test İlçe',
            'status' => true,
        ]);

        // Get roles
        $salesRepRole = Role::where('name', Role::SALES_REPRESENTATIVE)->first();
        $adminRole = Role::where('name', Role::ADMIN)->first();

        // Create a sales representative
        $salesRep = User::create([
            'name' => 'Satış Temsilcisi',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => bcrypt('password'),
            'role_id' => $salesRepRole->id,
            'dealer_id' => $dealer->id,
        ]);

        // Create an admin user assigned to the same dealer (should not be included)
        $admin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => bcrypt('password'),
            'role_id' => $adminRole->id,
            'dealer_id' => $dealer->id,
        ]);

        // Test that only sales representatives are returned
        $salesReps = $dealer->salesRepresentatives;
        $this->assertCount(1, $salesReps);
        $this->assertTrue($salesReps->contains($salesRep));
        $this->assertFalse($salesReps->contains($admin));
    }
}
